#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
A股板块历史数据获取工具 - 增强版
支持自定义时间范围、更多板块代码、智能重试机制

作者: AI助手
创建时间: 2025-01-22
修改时间: 2025-01-22
"""

import requests
import json
import pandas as pd
import os
import time
from datetime import datetime
from typing import List, Dict, Optional, Tuple
import logging
from concurrent.futures import ThreadPoolExecutor, as_completed
import random

class AdvancedSectorDataFetcher:
    """A股板块数据获取器 - 增强版"""

    def __init__(self):
        self.base_url = "https://push2his.eastmoney.com/api/qt/stock/kline/get"
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
            'Referer': 'https://quote.eastmoney.com/',
            'Accept': 'application/json, text/plain, */*'
        })

        # 设置日志
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler('sector_data.log', encoding='utf-8'),
                logging.StreamHandler()
            ]
        )
        self.logger = logging.getLogger(__name__)

        # 创建数据目录
        self.data_dir = "data"
        os.makedirs(self.data_dir, exist_ok=True)

        # 固定的时间范围设置
        self.start_date = "2024-01-01"
        self.end_date = "2024-12-31"

        # 从JSON文件加载板块代码
        self.sector_codes = self._load_sector_codes_from_json()

    def _load_sector_codes_from_json(self) -> Dict[str, str]:
        """从JSON文件加载板块代码列表"""
        json_file_path = "merged_sectors_data.json"
        sector_codes = {}

        try:
            with open(json_file_path, 'r', encoding='utf-8') as f:
                data = json.load(f)

            # 提取每个板块的代码和名称
            if 'sectors' in data:
                for sector in data['sectors']:
                    if 'sector_info' in sector:
                        sector_info = sector['sector_info']
                        sector_code = sector_info.get('sector_code')
                        sector_name = sector_info.get('sector_name')

                        if sector_code and sector_name:
                            sector_codes[sector_code] = sector_name

            self.logger.info(f"成功从 {json_file_path} 加载 {len(sector_codes)} 个板块代码")

        except FileNotFoundError:
            self.logger.error(f"未找到文件: {json_file_path}")
            # 使用备用的少量板块代码
            sector_codes = {
                "BK0001": "种植业与林业",
                "BK0020": "计算机应用",
                "BK0030": "银行",
                "BK0024": "医药制造",
                "BK0016": "汽车行业"
            }
            self.logger.warning(f"使用备用板块代码列表，共 {len(sector_codes)} 个板块")

        except json.JSONDecodeError as e:
            self.logger.error(f"JSON文件解析失败: {e}")
            sector_codes = {}

        except Exception as e:
            self.logger.error(f"加载板块代码时发生错误: {e}")
            sector_codes = {}

        return sector_codes

    def fetch_sector_data_by_date_range(self, sector_code: str, start_date: str, end_date: str, max_retries: int = 3) -> Optional[List[Dict]]:
        """
        根据日期范围获取板块数据
        
        Args:
            sector_code: 板块代码
            start_date: 开始日期 (YYYY-MM-DD)
            end_date: 结束日期 (YYYY-MM-DD)
            max_retries: 最大重试次数
            
        Returns:
            解析后的K线数据列表
        """
        for attempt in range(max_retries):
            try:
                # 计算需要获取的天数
                start_dt = datetime.strptime(start_date, '%Y-%m-%d')
                end_dt = datetime.strptime(end_date, '%Y-%m-%d')
                days_diff = (end_dt - start_dt).days + 50  # 多获取一些数据以确保覆盖范围
                
                # 构建API URL
                params = {
                    'secid': f'90.{sector_code}',
                    'fields1': 'f1,f2,f3,f4,f5,f6',
                    'fields2': 'f51,f52,f53,f54,f55,f56,f57,f58,f59,f60,f61',
                    'klt': '101',  # 日K线
                    'fqt': '1',    # 前复权
                    'end': end_date.replace('-', ''),  # 转换为YYYYMMDD格式
                    'lmt': str(min(days_diff, 1000)),  # API限制最多1000条
                    '_': str(int(time.time() * 1000))
                }
                
                response = self.session.get(self.base_url, params=params, timeout=15)
                response.raise_for_status()
                
                data = response.json()
                
                if data and data.get('data') and data['data'].get('klines'):
                    parsed_data = self.parse_kline_data(data['data']['klines'], sector_code)
                    # 过滤日期范围
                    filtered_data = self.filter_data_by_date_range(parsed_data, start_date, end_date)
                    return filtered_data
                else:
                    self.logger.warning(f"板块 {sector_code} 无数据返回 (尝试 {attempt + 1}/{max_retries})")
                    
            except requests.RequestException as e:
                self.logger.warning(f"获取板块 {sector_code} 数据失败 (尝试 {attempt + 1}/{max_retries}): {e}")
                if attempt < max_retries - 1:
                    time.sleep(random.uniform(1, 3))  # 随机延迟重试
            except Exception as e:
                self.logger.error(f"解析板块 {sector_code} 数据失败: {e}")
                break
        
        return None

    def filter_data_by_date_range(self, data: List[Dict], start_date: str, end_date: str) -> List[Dict]:
        """
        根据日期范围过滤数据
        
        Args:
            data: 原始数据列表
            start_date: 开始日期 (YYYY-MM-DD)
            end_date: 结束日期 (YYYY-MM-DD)
            
        Returns:
            过滤后的数据列表
        """
        filtered_data = []
        start_dt = datetime.strptime(start_date, '%Y-%m-%d')
        end_dt = datetime.strptime(end_date, '%Y-%m-%d')
        
        for item in data:
            try:
                item_date = datetime.strptime(item['date'], '%Y-%m-%d')
                if start_dt <= item_date <= end_dt:
                    filtered_data.append(item)
            except ValueError:
                continue
        
        return filtered_data

    def parse_kline_data(self, klines: List[str], sector_code: str) -> List[Dict]:
        """解析K线数据"""
        parsed_data = []
        
        for kline in klines:
            try:
                parts = kline.split(',')
                if len(parts) >= 11:
                    data_point = {
                        'sector_code': sector_code,
                        'sector_name': self.sector_codes.get(sector_code, '未知板块'),
                        'date': parts[0],
                        'open': float(parts[1]),
                        'close': float(parts[2]),
                        'high': float(parts[3]),
                        'low': float(parts[4]),
                        'volume': int(parts[5]),
                        'amount': float(parts[6]),
                        'amplitude': float(parts[7]),
                        'change_pct': float(parts[8]),
                        'change_amount': float(parts[9]),
                        'turnover_rate': float(parts[10])
                    }
                    parsed_data.append(data_point)
            except (ValueError, IndexError) as e:
                self.logger.warning(f"解析K线数据失败: {kline}, 错误: {e}")
                continue
                
        return parsed_data

    def fetch_all_sectors_by_date_range(self, start_date: str, end_date: str, max_workers: int = 3) -> Dict[str, List[Dict]]:
        """
        批量获取指定日期范围内所有板块数据
        
        Args:
            start_date: 开始日期 (YYYY-MM-DD)
            end_date: 结束日期 (YYYY-MM-DD)
            max_workers: 最大并发数
            
        Returns:
            所有板块数据字典
        """
        all_data = {}
        
        self.logger.info(f"开始获取 {start_date} 至 {end_date} 期间 {len(self.sector_codes)} 个板块的历史数据...")
        
        with ThreadPoolExecutor(max_workers=max_workers) as executor:
            # 提交所有任务
            future_to_sector = {
                executor.submit(self.fetch_sector_data_by_date_range, sector_code, start_date, end_date): sector_code
                for sector_code in self.sector_codes.keys()
            }
            
            # 处理完成的任务
            completed = 0
            for future in as_completed(future_to_sector):
                sector_code = future_to_sector[future]
                try:
                    data = future.result()
                    if data:
                        all_data[sector_code] = data
                        self.logger.info(f"✓ {sector_code} ({self.sector_codes[sector_code]}) - {len(data)}条数据")
                    else:
                        self.logger.warning(f"✗ {sector_code} ({self.sector_codes[sector_code]}) - 无数据")
                except Exception as e:
                    self.logger.error(f"✗ {sector_code} 获取失败: {e}")
                
                completed += 1
                self.logger.info(f"进度: {completed}/{len(self.sector_codes)} ({completed/len(self.sector_codes)*100:.1f}%)")
                
                # 添加随机延迟避免API限制
                time.sleep(random.uniform(0.2, 0.5))
        
        return all_data

    def save_data_with_summary(self, data: Dict[str, List[Dict]], start_date: str, end_date: str) -> Tuple[List[str], Dict]:
        """
        保存数据并生成统计摘要 - 同时保存CSV和JSON格式

        Args:
            data: 板块数据字典
            start_date: 开始日期
            end_date: 结束日期

        Returns:
            (文件路径列表, 统计摘要)
        """
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        saved_files = []

        # 生成统计摘要
        summary = self.generate_summary(data, start_date, end_date)

        # 合并所有数据
        all_records = []
        for sector_data in data.values():
            all_records.extend(sector_data)

        if all_records:
            # 保存CSV格式
            df = pd.DataFrame(all_records)
            # 按日期排序
            df['date'] = pd.to_datetime(df['date'])
            df = df.sort_values(['date', 'sector_code'])

            csv_filename = f"sector_data_{start_date}_to_{end_date}_{timestamp}.csv"
            csv_filepath = os.path.join(self.data_dir, csv_filename)
            df.to_csv(csv_filepath, index=False, encoding='utf-8-sig')
            saved_files.append(csv_filepath)
            self.logger.info(f"CSV数据已保存到: {csv_filepath}")

            # 保存JSON格式
            json_filename = f"sector_data_{start_date}_to_{end_date}_{timestamp}.json"
            json_filepath = os.path.join(self.data_dir, json_filename)
            with open(json_filepath, 'w', encoding='utf-8') as f:
                json.dump(data, f, ensure_ascii=False, indent=2)
            saved_files.append(json_filepath)
            self.logger.info(f"JSON数据已保存到: {json_filepath}")

            # 保存统计摘要
            summary_filename = f"summary_{start_date}_to_{end_date}_{timestamp}.json"
            summary_filepath = os.path.join(self.data_dir, summary_filename)
            with open(summary_filepath, 'w', encoding='utf-8') as f:
                json.dump(summary, f, ensure_ascii=False, indent=2)
            saved_files.append(summary_filepath)
            self.logger.info(f"统计摘要已保存到: {summary_filepath}")

        return saved_files, summary

    def generate_summary(self, data: Dict[str, List[Dict]], start_date: str, end_date: str) -> Dict:
        """生成数据统计摘要"""
        total_records = sum(len(sector_data) for sector_data in data.values())
        successful_sectors = len([k for k, v in data.items() if v])
        
        # 计算平均涨跌幅
        all_change_pcts = []
        for sector_data in data.values():
            for record in sector_data:
                all_change_pcts.append(record['change_pct'])
        
        avg_change_pct = sum(all_change_pcts) / len(all_change_pcts) if all_change_pcts else 0
        
        summary = {
            'date_range': f"{start_date} 至 {end_date}",
            'total_sectors': len(self.sector_codes),
            'successful_sectors': successful_sectors,
            'total_records': total_records,
            'average_change_pct': round(avg_change_pct, 2),
            'data_quality': f"{successful_sectors/len(self.sector_codes)*100:.1f}%",
            'generated_at': datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        }
        
        return summary


def main():
    """主程序入口 - 无用户交互版本"""
    print("=" * 70)
    print("A股板块历史数据获取工具 - 增强版")
    print("自动获取板块数据，无需用户交互")
    print("=" * 70)

    fetcher = AdvancedSectorDataFetcher()

    # 使用固定的时间范围
    start_date = fetcher.start_date
    end_date = fetcher.end_date

    print(f"时间范围: {start_date} 至 {end_date}")
    print(f"保存格式: CSV + JSON")
    print(f"板块数量: {len(fetcher.sector_codes)} 个")
    print(f"数据源: merged_sectors_data.json")
    print("-" * 70)

    # 获取数据
    start_time = time.time()
    all_data = fetcher.fetch_all_sectors_by_date_range(start_date, end_date)

    if all_data:
        # 保存数据 - 同时保存CSV和JSON格式
        saved_files, summary = fetcher.save_data_with_summary(all_data, start_date, end_date)

        elapsed_time = time.time() - start_time

        print("-" * 70)
        print(f"✓ 数据获取完成!")
        print(f"✓ 成功获取 {summary['successful_sectors']} 个板块数据")
        print(f"✓ 总计 {summary['total_records']} 条记录")
        print(f"✓ 数据质量: {summary['data_quality']}")
        print(f"✓ 平均涨跌幅: {summary['average_change_pct']}%")
        print(f"✓ 用时 {elapsed_time:.1f} 秒")
        print(f"✓ 保存的文件:")
        for file_path in saved_files:
            print(f"   - {file_path}")
        print("=" * 70)
        print("程序执行完成，数据已自动保存！")
    else:
        print("✗ 未获取到任何数据，请检查网络连接或API状态")
        print("✗ 程序执行失败！")


if __name__ == "__main__":
    main()

#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
验证advanced_sector_fetcher.py修改是否成功

作者: AI助手
创建时间: 2025-01-22
"""

import json
import os

def verify_json_file():
    """验证JSON文件是否存在且格式正确"""
    print("1. 验证JSON数据源文件...")
    
    json_file = "merged_sectors_data.json"
    if not os.path.exists(json_file):
        print(f"   ❌ 文件不存在: {json_file}")
        return False
    
    try:
        with open(json_file, 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        if 'sectors' not in data:
            print(f"   ❌ JSON文件格式错误，缺少'sectors'字段")
            return False
        
        sectors_count = len(data['sectors'])
        print(f"   ✅ JSON文件正常，包含 {sectors_count} 个板块")
        return True
        
    except Exception as e:
        print(f"   ❌ JSON文件读取失败: {e}")
        return False

def verify_code_modifications():
    """验证代码修改是否正确"""
    print("2. 验证代码修改...")
    
    try:
        # 读取修改后的文件
        with open('advanced_sector_fetcher.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查关键修改点
        checks = [
            ('_load_sector_codes_from_json', '✅ 新增JSON加载函数'),
            ('self.start_date = "2024-01-01"', '✅ 固定开始日期'),
            ('self.end_date = "2024-12-31"', '✅ 固定结束日期'),
            ('merged_sectors_data.json', '✅ 使用正确的JSON文件路径'),
            ('无用户交互版本', '✅ 移除用户交互'),
            ('CSV + JSON', '✅ 同时保存两种格式')
        ]
        
        all_passed = True
        for check_str, message in checks:
            if check_str in content:
                print(f"   {message}")
            else:
                print(f"   ❌ 缺少: {check_str}")
                all_passed = False
        
        return all_passed
        
    except Exception as e:
        print(f"   ❌ 代码验证失败: {e}")
        return False

def verify_class_initialization():
    """验证类是否能正常初始化"""
    print("3. 验证类初始化...")
    
    try:
        from advanced_sector_fetcher import AdvancedSectorDataFetcher
        
        # 创建实例（不输出日志）
        import logging
        logging.getLogger().setLevel(logging.ERROR)
        
        fetcher = AdvancedSectorDataFetcher()
        
        # 检查关键属性
        if not hasattr(fetcher, 'start_date'):
            print("   ❌ 缺少start_date属性")
            return False
        
        if not hasattr(fetcher, 'end_date'):
            print("   ❌ 缺少end_date属性")
            return False
        
        if not hasattr(fetcher, 'sector_codes'):
            print("   ❌ 缺少sector_codes属性")
            return False
        
        if not isinstance(fetcher.sector_codes, dict):
            print("   ❌ sector_codes不是字典类型")
            return False
        
        if len(fetcher.sector_codes) == 0:
            print("   ❌ 未加载任何板块代码")
            return False
        
        print(f"   ✅ 类初始化成功")
        print(f"   ✅ 时间范围: {fetcher.start_date} 至 {fetcher.end_date}")
        print(f"   ✅ 板块数量: {len(fetcher.sector_codes)} 个")
        
        # 显示前5个板块作为示例
        sample_sectors = list(fetcher.sector_codes.items())[:5]
        print(f"   ✅ 板块示例: {sample_sectors}")
        
        return True
        
    except Exception as e:
        print(f"   ❌ 类初始化失败: {e}")
        return False

def main():
    """主验证函数"""
    print("=" * 60)
    print("验证 advanced_sector_fetcher.py 修改结果")
    print("=" * 60)
    
    # 执行所有验证
    json_ok = verify_json_file()
    code_ok = verify_code_modifications()
    init_ok = verify_class_initialization()
    
    print("\n" + "=" * 60)
    print("验证结果汇总:")
    print("=" * 60)
    
    if json_ok and code_ok and init_ok:
        print("✅ 所有验证通过！")
        print("✅ 修改成功，程序可以正常运行")
        print("\n推荐操作:")
        print("1. 运行: python advanced_sector_fetcher.py")
        print("2. 或运行: run_advanced.bat")
        print("3. 程序将自动获取2024年全年的86个板块数据")
        print("4. 数据将同时保存为CSV和JSON格式")
    else:
        print("❌ 部分验证失败")
        print("❌ 请检查上述错误信息并修复")
    
    print("=" * 60)

if __name__ == "__main__":
    main()

#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试JSON文件加载功能
验证是否能正确从merged_sectors_data.json读取板块代码

作者: AI助手
创建时间: 2025-01-22
"""

import json
import os

def test_json_loading():
    """测试JSON文件加载"""
    json_file_path = "merged_sectors_data.json"
    
    print("=" * 60)
    print("测试JSON文件加载功能")
    print("=" * 60)
    
    # 检查文件是否存在
    if not os.path.exists(json_file_path):
        print(f"❌ 文件不存在: {json_file_path}")
        return False
    
    print(f"✅ 文件存在: {json_file_path}")
    
    try:
        # 读取JSON文件
        with open(json_file_path, 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        print(f"✅ JSON文件读取成功")
        
        # 检查数据结构
        if 'sectors' not in data:
            print("❌ JSON文件中没有'sectors'字段")
            return False
        
        sectors = data['sectors']
        print(f"✅ 找到sectors字段，包含 {len(sectors)} 个板块")
        
        # 提取板块代码
        sector_codes = {}
        valid_count = 0
        
        for i, sector in enumerate(sectors):
            if 'sector_info' in sector:
                sector_info = sector['sector_info']
                sector_code = sector_info.get('sector_code')
                sector_name = sector_info.get('sector_name')
                
                if sector_code and sector_name:
                    sector_codes[sector_code] = sector_name
                    valid_count += 1
                    
                    # 显示前10个板块作为示例
                    if i < 10:
                        print(f"  {i+1:2d}. {sector_code}: {sector_name}")
        
        print(f"✅ 成功提取 {valid_count} 个有效板块代码")
        
        if valid_count > 10:
            print(f"  ... (还有 {valid_count - 10} 个板块)")
        
        # 显示一些统计信息
        print(f"\n板块代码统计:")
        print(f"- 总数量: {len(sector_codes)}")
        print(f"- 代码格式: {list(sector_codes.keys())[:5]}...")
        print(f"- 名称示例: {list(sector_codes.values())[:5]}...")
        
        return True
        
    except json.JSONDecodeError as e:
        print(f"❌ JSON解析失败: {e}")
        return False
    except Exception as e:
        print(f"❌ 读取文件时发生错误: {e}")
        return False

def test_advanced_fetcher_init():
    """测试AdvancedSectorDataFetcher初始化"""
    print("\n" + "=" * 60)
    print("测试AdvancedSectorDataFetcher初始化")
    print("=" * 60)
    
    try:
        # 导入修改后的类
        from advanced_sector_fetcher import AdvancedSectorDataFetcher
        
        # 创建实例
        fetcher = AdvancedSectorDataFetcher()
        
        print(f"✅ AdvancedSectorDataFetcher初始化成功")
        print(f"✅ 加载板块数量: {len(fetcher.sector_codes)}")
        print(f"✅ 固定开始日期: {fetcher.start_date}")
        print(f"✅ 固定结束日期: {fetcher.end_date}")
        
        # 显示前10个板块
        print(f"\n前10个板块:")
        for i, (code, name) in enumerate(list(fetcher.sector_codes.items())[:10]):
            print(f"  {i+1:2d}. {code}: {name}")
        
        return True
        
    except ImportError as e:
        print(f"❌ 导入失败: {e}")
        return False
    except Exception as e:
        print(f"❌ 初始化失败: {e}")
        return False

def main():
    """主测试函数"""
    from datetime import datetime
    print(f"开始测试 - {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # 测试1: JSON文件加载
    json_test_success = test_json_loading()
    
    if json_test_success:
        # 测试2: 类初始化
        init_test_success = test_advanced_fetcher_init()
        
        if init_test_success:
            print("\n" + "=" * 60)
            print("✅ 所有测试通过!")
            print("✅ 修改后的程序可以正常运行")
            print("✅ 建议运行: python advanced_sector_fetcher.py")
            print("=" * 60)
        else:
            print("\n" + "=" * 60)
            print("❌ 类初始化测试失败")
            print("❌ 请检查代码修改是否正确")
            print("=" * 60)
    else:
        print("\n" + "=" * 60)
        print("❌ JSON文件加载测试失败")
        print("❌ 请检查merged_sectors_data.json文件")
        print("=" * 60)

if __name__ == "__main__":
    main()

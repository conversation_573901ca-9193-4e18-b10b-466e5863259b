#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
API接口测试脚本
验证东方财富板块数据API是否可用

作者: AI助手
创建时间: 2025-01-22
"""

import requests
import json
import time
from datetime import datetime

def test_single_sector_api():
    """测试单个板块数据获取"""
    print("=" * 50)
    print("测试东方财富板块数据API")
    print("=" * 50)
    
    # 测试参数
    sector_code = "BK0001"  # 种植业与林业
    base_url = "https://push2his.eastmoney.com/api/qt/stock/kline/get"
    
    # 构建请求参数
    params = {
        'secid': f'90.{sector_code}',
        'fields1': 'f1,f2,f3,f4,f5,f6',
        'fields2': 'f51,f52,f53,f54,f55,f56,f57,f58,f59,f60,f61',
        'klt': '101',  # 日K线
        'fqt': '1',    # 前复权
        'end': '20500101',
        'lmt': '10',   # 只获取10条数据进行测试
        '_': str(int(time.time() * 1000))
    }
    
    # 设置请求头
    headers = {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
        'Referer': 'https://quote.eastmoney.com/',
        'Accept': 'application/json, text/plain, */*'
    }
    
    try:
        print(f"正在测试板块: {sector_code} (种植业与林业)")
        print(f"请求URL: {base_url}")
        print(f"请求参数: {params}")
        print("-" * 50)
        
        # 发送请求
        response = requests.get(base_url, params=params, headers=headers, timeout=10)
        
        print(f"响应状态码: {response.status_code}")
        print(f"响应头: {dict(response.headers)}")
        print("-" * 50)
        
        if response.status_code == 200:
            data = response.json()
            print("✓ API请求成功!")
            print(f"响应数据结构: {list(data.keys()) if isinstance(data, dict) else type(data)}")
            
            if data and data.get('data'):
                print(f"✓ 数据字段: {list(data['data'].keys())}")
                
                if data['data'].get('klines'):
                    klines = data['data']['klines']
                    print(f"✓ 获取到 {len(klines)} 条K线数据")
                    
                    # 解析前3条数据作为示例
                    print("\n前3条数据示例:")
                    for i, kline in enumerate(klines[:3]):
                        parts = kline.split(',')
                        if len(parts) >= 11:
                            print(f"  {i+1}. 日期: {parts[0]}, 开盘: {parts[1]}, 收盘: {parts[2]}, "
                                  f"最高: {parts[3]}, 最低: {parts[4]}, 涨跌幅: {parts[8]}%")
                        else:
                            print(f"  {i+1}. 数据格式异常: {kline}")
                    
                    print("\n✓ API测试成功! 数据格式正确，可以正常获取板块历史数据")
                    return True
                else:
                    print("✗ 响应中没有klines数据")
                    print(f"完整响应: {json.dumps(data, ensure_ascii=False, indent=2)}")
            else:
                print("✗ 响应中没有data字段")
                print(f"完整响应: {json.dumps(data, ensure_ascii=False, indent=2)}")
        else:
            print(f"✗ API请求失败，状态码: {response.status_code}")
            print(f"响应内容: {response.text}")
            
    except requests.RequestException as e:
        print(f"✗ 网络请求异常: {e}")
    except json.JSONDecodeError as e:
        print(f"✗ JSON解析失败: {e}")
        print(f"响应内容: {response.text}")
    except Exception as e:
        print(f"✗ 未知错误: {e}")
    
    return False

def test_multiple_sectors():
    """测试多个板块数据获取"""
    print("\n" + "=" * 50)
    print("测试多个板块数据获取")
    print("=" * 50)
    
    test_sectors = {
        "BK0001": "种植业与林业",
        "BK0020": "计算机应用", 
        "BK0030": "银行",
        "BK0024": "医药制造",
        "BK0016": "汽车行业"
    }
    
    success_count = 0
    
    for sector_code, sector_name in test_sectors.items():
        print(f"\n测试板块: {sector_code} ({sector_name})")
        
        params = {
            'secid': f'90.{sector_code}',
            'fields1': 'f1,f2,f3,f4,f5,f6',
            'fields2': 'f51,f52,f53,f54,f55,f56,f57,f58,f59,f60,f61',
            'klt': '101',
            'fqt': '1',
            'end': '20500101',
            'lmt': '5',
            '_': str(int(time.time() * 1000))
        }
        
        try:
            response = requests.get(
                "https://push2his.eastmoney.com/api/qt/stock/kline/get",
                params=params,
                headers={
                    'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
                    'Referer': 'https://quote.eastmoney.com/',
                },
                timeout=10
            )
            
            if response.status_code == 200:
                data = response.json()
                if data and data.get('data') and data['data'].get('klines'):
                    klines_count = len(data['data']['klines'])
                    print(f"  ✓ 成功获取 {klines_count} 条数据")
                    success_count += 1
                else:
                    print(f"  ✗ 无数据返回")
            else:
                print(f"  ✗ 请求失败 (状态码: {response.status_code})")
                
        except Exception as e:
            print(f"  ✗ 异常: {e}")
        
        # 添加延迟避免频率限制
        time.sleep(0.5)
    
    print(f"\n测试结果: {success_count}/{len(test_sectors)} 个板块测试成功")
    
    if success_count == len(test_sectors):
        print("✓ 所有测试板块都能正常获取数据!")
        return True
    else:
        print("⚠ 部分板块获取失败，可能存在网络问题或API限制")
        return False

def test_date_range():
    """测试日期范围参数"""
    print("\n" + "=" * 50)
    print("测试日期范围参数")
    print("=" * 50)
    
    sector_code = "BK0001"
    
    # 测试不同的结束日期
    test_dates = [
        "20241231",  # 2024年末
        "20241201",  # 2024年12月
        "20241101",  # 2024年11月
    ]
    
    for end_date in test_dates:
        print(f"\n测试结束日期: {end_date}")
        
        params = {
            'secid': f'90.{sector_code}',
            'fields1': 'f1,f2,f3,f4,f5,f6',
            'fields2': 'f51,f52,f53,f54,f55,f56,f57,f58,f59,f60,f61',
            'klt': '101',
            'fqt': '1',
            'end': end_date,
            'lmt': '10',
            '_': str(int(time.time() * 1000))
        }
        
        try:
            response = requests.get(
                "https://push2his.eastmoney.com/api/qt/stock/kline/get",
                params=params,
                timeout=10
            )
            
            if response.status_code == 200:
                data = response.json()
                if data and data.get('data') and data['data'].get('klines'):
                    klines = data['data']['klines']
                    if klines:
                        latest_date = klines[0].split(',')[0]
                        oldest_date = klines[-1].split(',')[0]
                        print(f"  ✓ 获取 {len(klines)} 条数据")
                        print(f"  ✓ 日期范围: {oldest_date} 至 {latest_date}")
                    else:
                        print(f"  ✗ 无K线数据")
                else:
                    print(f"  ✗ 响应格式异常")
            else:
                print(f"  ✗ 请求失败")
                
        except Exception as e:
            print(f"  ✗ 异常: {e}")
        
        time.sleep(0.5)

def main():
    """主测试函数"""
    print(f"开始API测试 - {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # 测试1: 单个板块API
    single_test_success = test_single_sector_api()
    
    if single_test_success:
        # 测试2: 多个板块
        multiple_test_success = test_multiple_sectors()
        
        # 测试3: 日期范围
        test_date_range()
        
        print("\n" + "=" * 50)
        print("测试总结")
        print("=" * 50)
        print("✓ 东方财富板块数据API可用")
        print("✓ 数据格式正确，包含完整的K线信息")
        print("✓ 支持日期范围参数")
        print("✓ 可以批量获取多个板块数据")
        print("\n建议:")
        print("1. 请求间隔设置为0.2-0.5秒避免频率限制")
        print("2. 添加重试机制处理网络异常")
        print("3. 使用多线程时控制并发数量")
        print("\n可以开始使用主程序获取完整数据!")
        
    else:
        print("\n" + "=" * 50)
        print("测试失败")
        print("=" * 50)
        print("✗ API测试失败，可能的原因:")
        print("1. 网络连接问题")
        print("2. API接口变更")
        print("3. 访问频率限制")
        print("4. 请求参数错误")
        print("\n建议检查网络连接后重试")

if __name__ == "__main__":
    main()

# A股板块历史数据获取工具

基于东方财富API接口，批量获取A股市场86个板块的历史涨跌数据。

## 功能特性

### 基础版本 (`sector_data_fetcher.py`)
- ✅ 获取A股40个主要板块的历史数据
- ✅ 支持指定获取天数（默认20天）
- ✅ 多线程并发获取，提高效率
- ✅ 支持CSV、JSON、Excel三种保存格式
- ✅ 完整的错误处理和日志记录
- ✅ 实时进度显示

### 增强版本 (`advanced_sector_fetcher.py`)
- ✅ 支持86个板块的完整数据获取
- ✅ **自定义时间范围**：用户可指定开始和结束日期
- ✅ **智能重试机制**：网络异常自动重试
- ✅ **数据质量保证**：自动过滤和验证数据
- ✅ **统计摘要生成**：自动生成数据统计报告
- ✅ **随机延迟**：避免API频率限制
- ✅ **日期范围验证**：确保输入日期的合理性

## 安装依赖

```bash
pip install -r requirements.txt
```

或手动安装：

```bash
pip install requests pandas openpyxl
```

## 使用方法

### 基础版本使用
```bash
python sector_data_fetcher.py
```

按提示输入：
- 获取天数（默认20天）
- 保存格式（CSV/JSON/Excel）

### 增强版本使用（推荐）
```bash
python advanced_sector_fetcher.py
```

按提示输入：
- 开始日期（如：2024-01-01）
- 结束日期（如：2024-12-31）
- 保存格式（CSV/JSON/Excel）

## 数据字段说明

每条记录包含以下字段：

| 字段名 | 说明 | 示例 |
|--------|------|------|
| sector_code | 板块代码 | BK0001 |
| sector_name | 板块名称 | 种植业与林业 |
| date | 交易日期 | 2024-01-15 |
| open | 开盘价 | 1234.56 |
| close | 收盘价 | 1245.78 |
| high | 最高价 | 1250.00 |
| low | 最低价 | 1230.00 |
| volume | 成交量 | 1234567890 |
| amount | 成交额 | 12345678.90 |
| amplitude | 振幅(%) | 1.62 |
| change_pct | 涨跌幅(%) | 0.91 |
| change_amount | 涨跌额 | 11.22 |
| turnover_rate | 换手率(%) | 2.34 |

## 板块代码列表

工具支持86个A股主要板块，包括：

**申万一级行业分类：**
- BK0001: 种植业与林业
- BK0002: 养殖业
- BK0003: 农产品加工
- BK0004: 饮料制造
- BK0005: 纺织服装
- ... (完整列表见代码)

## 输出文件

### 数据文件
- **CSV格式**：`sector_data_YYYYMMDD_HHMMSS.csv`
- **JSON格式**：`sector_data_YYYYMMDD_HHMMSS.json`
- **Excel格式**：`sector_data_YYYYMMDD_HHMMSS.xlsx`

### 统计摘要（增强版）
- **摘要文件**：`summary_开始日期_to_结束日期_时间戳.json`

包含：
- 数据时间范围
- 成功获取的板块数量
- 总记录数
- 平均涨跌幅
- 数据质量评分

### 日志文件
- **日志文件**：`sector_data.log`

记录详细的执行过程和错误信息。

## API接口说明

### 东方财富K线数据接口
- **URL**: `https://push2his.eastmoney.com/api/qt/stock/kline/get`
- **方法**: GET
- **频率限制**: 建议每次请求间隔0.2-0.5秒

### 关键参数
- `secid=90.{板块代码}`: 90表示板块类型
- `klt=101`: 日K线数据
- `fqt=1`: 前复权
- `lmt={数量}`: 获取条数限制

## 注意事项

### API限制
1. **频率限制**：避免过于频繁的请求，建议间隔0.2秒以上
2. **数据量限制**：单次最多获取1000条记录
3. **时间限制**：只能获取历史数据，不包含实时数据

### 数据质量
1. **交易日过滤**：自动过滤非交易日数据
2. **数据验证**：自动验证数据格式和完整性
3. **异常处理**：网络异常自动重试，数据异常自动跳过

### 使用建议
1. **首次使用**：建议先用基础版本测试少量数据
2. **大量数据**：使用增强版本，支持断点续传
3. **网络环境**：确保网络连接稳定
4. **存储空间**：预留足够的磁盘空间

## 故障排除

### 常见问题

**1. 网络连接失败**
```
解决方案：
- 检查网络连接
- 尝试使用VPN
- 增加重试次数
```

**2. 数据获取不完整**
```
解决方案：
- 检查日期范围是否合理
- 确认板块代码是否正确
- 查看日志文件了解详细错误
```

**3. 保存文件失败**
```
解决方案：
- 检查磁盘空间
- 确认data目录权限
- 关闭可能占用文件的程序
```

## 版本历史

- **v1.0** (2025-01-22): 基础版本，支持40个板块
- **v2.0** (2025-01-22): 增强版本，支持86个板块和自定义时间范围

## 免责声明

本工具仅供学习和研究使用，获取的数据来源于公开API接口。使用者应：

1. 遵守相关法律法规
2. 尊重数据提供方的使用条款
3. 不用于商业用途
4. 自行承担使用风险

数据的准确性和完整性不做保证，投资决策请以官方数据为准。

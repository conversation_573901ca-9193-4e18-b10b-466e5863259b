#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
A股板块历史数据获取工具
基于东方财富API接口，批量获取86个板块的历史涨跌数据

作者: AI助手
创建时间: 2025-01-22
"""

import requests
import json
import pandas as pd
import os
import time
from datetime import datetime, timedelta
from typing import List, Dict, Optional, Tuple
import logging
from concurrent.futures import ThreadPoolExecutor, as_completed

class SectorDataFetcher:
    """A股板块数据获取器"""
    
    def __init__(self):
        self.base_url = "https://push2his.eastmoney.com/api/qt/stock/kline/get"
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
            'Referer': 'https://quote.eastmoney.com/',
            'Accept': 'application/json, text/plain, */*'
        })
        
        # 设置日志
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler('sector_data.log', encoding='utf-8'),
                logging.StreamHandler()
            ]
        )
        self.logger = logging.getLogger(__name__)
        
        # 创建数据目录
        self.data_dir = "data"
        os.makedirs(self.data_dir, exist_ok=True)
        
        # A股主要板块代码（示例，可扩展）
        self.sector_codes = {
            "BK0001": "种植业与林业",
            "BK0002": "养殖业", 
            "BK0003": "农产品加工",
            "BK0004": "饮料制造",
            "BK0005": "纺织服装",
            "BK0006": "造纸印刷",
            "BK0007": "石油化工",
            "BK0008": "塑料制品",
            "BK0009": "钢铁行业",
            "BK0010": "有色金属",
            "BK0011": "建筑材料",
            "BK0012": "建筑装饰",
            "BK0013": "电气设备",
            "BK0014": "机械设备",
            "BK0015": "国防军工",
            "BK0016": "汽车行业",
            "BK0017": "船舶制造",
            "BK0018": "航空航天",
            "BK0019": "电子元件",
            "BK0020": "计算机应用",
            "BK0021": "通信设备",
            "BK0022": "家用电器",
            "BK0023": "食品饮料",
            "BK0024": "医药制造",
            "BK0025": "公用事业",
            "BK0026": "交通运输",
            "BK0027": "仓储物流",
            "BK0028": "批发零售",
            "BK0029": "房地产",
            "BK0030": "银行",
            "BK0031": "保险",
            "BK0032": "证券",
            "BK0033": "多元金融",
            "BK0034": "传媒",
            "BK0035": "互联网服务",
            "BK0036": "软件开发",
            "BK0037": "环保工程",
            "BK0038": "美容护理",
            "BK0039": "旅游酒店",
            "BK0040": "文化体育"
        }

    def fetch_sector_data(self, sector_code: str, days: int = 20) -> Optional[List[Dict]]:
        """
        获取单个板块的历史数据
        
        Args:
            sector_code: 板块代码
            days: 获取天数
            
        Returns:
            解析后的K线数据列表
        """
        try:
            # 构建API URL
            params = {
                'secid': f'90.{sector_code}',
                'fields1': 'f1,f2,f3,f4,f5,f6',
                'fields2': 'f51,f52,f53,f54,f55,f56,f57,f58,f59,f60,f61',
                'klt': '101',  # 日K线
                'fqt': '1',    # 前复权
                'end': '20500101',
                'lmt': str(days),
                '_': str(int(time.time() * 1000))
            }
            
            response = self.session.get(self.base_url, params=params, timeout=10)
            response.raise_for_status()
            
            data = response.json()
            
            if data and data.get('data') and data['data'].get('klines'):
                return self.parse_kline_data(data['data']['klines'], sector_code)
            else:
                self.logger.warning(f"板块 {sector_code} 无数据返回")
                return None
                
        except requests.RequestException as e:
            self.logger.error(f"获取板块 {sector_code} 数据失败: {e}")
            return None
        except Exception as e:
            self.logger.error(f"解析板块 {sector_code} 数据失败: {e}")
            return None

    def parse_kline_data(self, klines: List[str], sector_code: str) -> List[Dict]:
        """
        解析K线数据
        
        Args:
            klines: K线数据字符串列表
            sector_code: 板块代码
            
        Returns:
            解析后的数据列表
        """
        parsed_data = []
        
        for kline in klines:
            try:
                # K线数据格式: 日期,开盘,收盘,最高,最低,成交量,成交额,振幅,涨跌幅,涨跌额,换手率
                parts = kline.split(',')
                if len(parts) >= 11:
                    data_point = {
                        'sector_code': sector_code,
                        'sector_name': self.sector_codes.get(sector_code, '未知板块'),
                        'date': parts[0],
                        'open': float(parts[1]),
                        'close': float(parts[2]),
                        'high': float(parts[3]),
                        'low': float(parts[4]),
                        'volume': int(parts[5]),
                        'amount': float(parts[6]),
                        'amplitude': float(parts[7]),
                        'change_pct': float(parts[8]),
                        'change_amount': float(parts[9]),
                        'turnover_rate': float(parts[10])
                    }
                    parsed_data.append(data_point)
            except (ValueError, IndexError) as e:
                self.logger.warning(f"解析K线数据失败: {kline}, 错误: {e}")
                continue
                
        return parsed_data

    def fetch_all_sectors_data(self, days: int = 20, max_workers: int = 5) -> Dict[str, List[Dict]]:
        """
        批量获取所有板块数据
        
        Args:
            days: 获取天数
            max_workers: 最大并发数
            
        Returns:
            所有板块数据字典
        """
        all_data = {}
        
        self.logger.info(f"开始获取 {len(self.sector_codes)} 个板块的历史数据...")
        
        with ThreadPoolExecutor(max_workers=max_workers) as executor:
            # 提交所有任务
            future_to_sector = {
                executor.submit(self.fetch_sector_data, sector_code, days): sector_code
                for sector_code in self.sector_codes.keys()
            }
            
            # 处理完成的任务
            completed = 0
            for future in as_completed(future_to_sector):
                sector_code = future_to_sector[future]
                try:
                    data = future.result()
                    if data:
                        all_data[sector_code] = data
                        self.logger.info(f"✓ {sector_code} ({self.sector_codes[sector_code]}) - {len(data)}条数据")
                    else:
                        self.logger.warning(f"✗ {sector_code} ({self.sector_codes[sector_code]}) - 无数据")
                except Exception as e:
                    self.logger.error(f"✗ {sector_code} 获取失败: {e}")
                
                completed += 1
                self.logger.info(f"进度: {completed}/{len(self.sector_codes)} ({completed/len(self.sector_codes)*100:.1f}%)")
                
                # 添加延迟避免API限制
                time.sleep(0.1)
        
        return all_data

    def save_data(self, data: Dict[str, List[Dict]], format_type: str = 'csv') -> str:
        """
        保存数据到文件
        
        Args:
            data: 板块数据字典
            format_type: 保存格式 ('csv', 'json', 'excel')
            
        Returns:
            保存的文件路径
        """
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        
        if format_type.lower() == 'csv':
            # 合并所有数据到一个DataFrame
            all_records = []
            for sector_data in data.values():
                all_records.extend(sector_data)
            
            if all_records:
                df = pd.DataFrame(all_records)
                filename = f"sector_data_{timestamp}.csv"
                filepath = os.path.join(self.data_dir, filename)
                df.to_csv(filepath, index=False, encoding='utf-8-sig')
                self.logger.info(f"数据已保存到: {filepath}")
                return filepath
                
        elif format_type.lower() == 'json':
            filename = f"sector_data_{timestamp}.json"
            filepath = os.path.join(self.data_dir, filename)
            with open(filepath, 'w', encoding='utf-8') as f:
                json.dump(data, f, ensure_ascii=False, indent=2)
            self.logger.info(f"数据已保存到: {filepath}")
            return filepath
            
        elif format_type.lower() == 'excel':
            filename = f"sector_data_{timestamp}.xlsx"
            filepath = os.path.join(self.data_dir, filename)
            
            with pd.ExcelWriter(filepath, engine='openpyxl') as writer:
                # 创建汇总表
                all_records = []
                for sector_data in data.values():
                    all_records.extend(sector_data)
                
                if all_records:
                    df_summary = pd.DataFrame(all_records)
                    df_summary.to_excel(writer, sheet_name='汇总数据', index=False)
                
                # 为每个板块创建单独的工作表
                for sector_code, sector_data in data.items():
                    if sector_data:
                        df_sector = pd.DataFrame(sector_data)
                        sheet_name = f"{sector_code}_{self.sector_codes.get(sector_code, '未知')[:10]}"
                        df_sector.to_excel(writer, sheet_name=sheet_name, index=False)
            
            self.logger.info(f"数据已保存到: {filepath}")
            return filepath
        
        return ""


def main():
    """主程序入口"""
    print("=" * 60)
    print("A股板块历史数据获取工具")
    print("=" * 60)
    
    fetcher = SectorDataFetcher()
    
    # 用户输入
    try:
        days = int(input("请输入要获取的历史天数 (默认20天): ") or "20")
        if days <= 0 or days > 1000:
            print("天数范围应在1-1000之间，使用默认值20天")
            days = 20
    except ValueError:
        print("输入无效，使用默认值20天")
        days = 20
    
    print(f"\n选择保存格式:")
    print("1. CSV格式 (推荐)")
    print("2. JSON格式")
    print("3. Excel格式")
    
    format_choice = input("请选择 (1-3, 默认1): ") or "1"
    format_map = {"1": "csv", "2": "json", "3": "excel"}
    save_format = format_map.get(format_choice, "csv")
    
    print(f"\n开始获取最近 {days} 天的板块数据...")
    print(f"保存格式: {save_format.upper()}")
    print("-" * 60)
    
    # 获取数据
    start_time = time.time()
    all_data = fetcher.fetch_all_sectors_data(days=days)
    
    if all_data:
        # 保存数据
        filepath = fetcher.save_data(all_data, save_format)
        
        # 统计信息
        total_records = sum(len(sector_data) for sector_data in all_data.values())
        elapsed_time = time.time() - start_time
        
        print("-" * 60)
        print(f"✓ 数据获取完成!")
        print(f"✓ 成功获取 {len(all_data)} 个板块数据")
        print(f"✓ 总计 {total_records} 条记录")
        print(f"✓ 用时 {elapsed_time:.1f} 秒")
        print(f"✓ 文件保存至: {filepath}")
        print("=" * 60)
    else:
        print("✗ 未获取到任何数据，请检查网络连接或API状态")


if __name__ == "__main__":
    main()

@echo off
chcp 65001 >nul
echo ========================================
echo A股板块历史数据获取工具 - 安装脚本
echo ========================================
echo.

echo 正在检查Python环境...
python --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ 未找到Python环境
    echo 请先安装Python 3.7或更高版本
    echo 下载地址: https://www.python.org/downloads/
    pause
    exit /b 1
)

echo ✅ Python环境检查通过
echo.

echo 正在安装依赖库...
pip install -r requirements.txt

if %errorlevel% neq 0 (
    echo ❌ 依赖库安装失败
    echo 请检查网络连接或手动安装:
    echo pip install requests pandas openpyxl
    pause
    exit /b 1
)

echo ✅ 依赖库安装完成
echo.

echo 正在测试API连接...
python test_api.py

echo.
echo ========================================
echo 安装完成！
echo ========================================
echo.
echo 使用方法:
echo 1. 基础版本: python sector_data_fetcher.py
echo 2. 增强版本: python advanced_sector_fetcher.py
echo 3. API测试:   python test_api.py
echo.
echo 数据将保存在 data 文件夹中
echo 详细说明请查看 README.md
echo.
pause
